#!/usr/bin/env python3
"""
WebSocket架构设计问题精确诊断脚本
分析多协程并发访问同一WebSocket连接的问题
"""

import os
import sys
import json
import re
import time
from pathlib import Path
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class WebSocketArchitectureDiagnostic:
    """WebSocket架构诊断器"""
    
    def __init__(self):
        self.project_root = project_root
        self.issues = []
        self.analysis_results = {}
        
    def analyze_websocket_architecture(self):
        """分析WebSocket架构设计问题"""
        print("🔍 开始WebSocket架构设计问题诊断...")
        
        # 1. 分析OKX WebSocket实现
        self._analyze_okx_websocket()
        
        # 2. 分析Gate.io WebSocket实现
        self._analyze_gate_websocket()
        
        # 3. 分析Bybit WebSocket实现（参考标准）
        self._analyze_bybit_websocket()
        
        # 4. 分析基类WebSocket客户端
        self._analyze_base_websocket_client()
        
        # 5. 分析连接池管理器
        self._analyze_connection_pool_manager()
        
        # 6. 生成诊断报告
        self._generate_diagnosis_report()
        
    def _analyze_okx_websocket(self):
        """分析OKX WebSocket实现"""
        print("📊 分析OKX WebSocket实现...")
        
        okx_file = self.project_root / "websocket" / "okx_ws.py"
        if not okx_file.exists():
            self.issues.append({
                "type": "CRITICAL",
                "component": "OKX WebSocket",
                "issue": "okx_ws.py文件不存在",
                "impact": "无法分析OKX WebSocket架构"
            })
            return
            
        content = okx_file.read_text(encoding='utf-8')
        
        # 检查run()方法实现
        run_method_issues = self._check_run_method(content, "OKX")
        
        # 检查是否存在_monitor_data_flow方法
        monitor_issues = self._check_monitoring_tasks(content, "OKX")
        
        # 检查订阅机制中的并发处理
        subscription_issues = self._check_subscription_concurrency(content, "OKX")
        
        # 检查健康监控配置
        health_monitor_issues = self._check_health_monitoring(content, "OKX")
        
        self.analysis_results["okx"] = {
            "run_method": run_method_issues,
            "monitoring_tasks": monitor_issues,
            "subscription_concurrency": subscription_issues,
            "health_monitoring": health_monitor_issues
        }
        
    def _analyze_gate_websocket(self):
        """分析Gate.io WebSocket实现"""
        print("📊 分析Gate.io WebSocket实现...")
        
        gate_file = self.project_root / "websocket" / "gate_ws.py"
        if not gate_file.exists():
            self.issues.append({
                "type": "CRITICAL",
                "component": "Gate.io WebSocket",
                "issue": "gate_ws.py文件不存在",
                "impact": "无法分析Gate.io WebSocket架构"
            })
            return
            
        content = gate_file.read_text(encoding='utf-8')
        
        # 检查run()方法实现
        run_method_issues = self._check_run_method(content, "Gate.io")
        
        # 检查心跳间隔配置
        heartbeat_issues = self._check_heartbeat_configuration(content, "Gate.io")
        
        # 检查消息处理限流
        throttling_issues = self._check_message_throttling(content, "Gate.io")
        
        self.analysis_results["gate"] = {
            "run_method": run_method_issues,
            "heartbeat_config": heartbeat_issues,
            "message_throttling": throttling_issues
        }
        
    def _analyze_bybit_websocket(self):
        """分析Bybit WebSocket实现（参考标准）"""
        print("📊 分析Bybit WebSocket实现（参考标准）...")
        
        bybit_file = self.project_root / "websocket" / "bybit_ws.py"
        if not bybit_file.exists():
            self.issues.append({
                "type": "CRITICAL",
                "component": "Bybit WebSocket",
                "issue": "bybit_ws.py文件不存在",
                "impact": "无法分析Bybit WebSocket架构"
            })
            return
            
        content = bybit_file.read_text(encoding='utf-8')
        
        # 检查简洁架构实现
        simplicity_score = self._check_architecture_simplicity(content, "Bybit")
        
        # 检查是否符合官方API规范
        api_compliance = self._check_api_compliance(content, "Bybit")
        
        self.analysis_results["bybit"] = {
            "architecture_simplicity": simplicity_score,
            "api_compliance": api_compliance,
            "status": "REFERENCE_STANDARD"
        }
        
    def _analyze_base_websocket_client(self):
        """分析基类WebSocket客户端"""
        print("📊 分析基类WebSocket客户端...")
        
        base_file = self.project_root / "websocket" / "ws_client.py"
        if not base_file.exists():
            self.issues.append({
                "type": "CRITICAL",
                "component": "Base WebSocket Client",
                "issue": "ws_client.py文件不存在",
                "impact": "无法分析基类WebSocket架构"
            })
            return
            
        content = base_file.read_text(encoding='utf-8')
        
        # 检查主消息循环中的recv()调用
        recv_issues = self._check_recv_calls(content)
        
        # 检查健康监控任务
        health_monitor_issues = self._check_base_health_monitoring(content)
        
        # 检查心跳任务
        heartbeat_issues = self._check_heartbeat_task(content)
        
        self.analysis_results["base_client"] = {
            "recv_calls": recv_issues,
            "health_monitoring": health_monitor_issues,
            "heartbeat_task": heartbeat_issues
        }
        
    def _analyze_connection_pool_manager(self):
        """分析连接池管理器"""
        print("📊 分析连接池管理器...")
        
        pool_file = self.project_root / "websocket" / "unified_connection_pool_manager.py"
        if not pool_file.exists():
            self.issues.append({
                "type": "WARNING",
                "component": "Connection Pool Manager",
                "issue": "unified_connection_pool_manager.py文件不存在",
                "impact": "连接池管理功能可能不可用"
            })
            return
            
        content = pool_file.read_text(encoding='utf-8')
        
        # 检查handle_connection_issue方法
        connection_issue_handling = self._check_connection_issue_handling(content)
        
        self.analysis_results["connection_pool"] = {
            "connection_issue_handling": connection_issue_handling
        }
        
    def _check_run_method(self, content: str, exchange: str) -> Dict[str, Any]:
        """检查run()方法实现"""
        issues = []
        
        # 查找run方法
        run_method_pattern = r'async def run\(self\):(.*?)(?=async def|\Z)'
        run_match = re.search(run_method_pattern, content, re.DOTALL)
        
        if not run_match:
            issues.append(f"{exchange}: 未找到run()方法")
            return {"issues": issues, "status": "CRITICAL"}
            
        run_method_content = run_match.group(1)
        
        # 检查是否调用了super().run()
        if "await super().run()" not in run_method_content:
            issues.append(f"{exchange}: run()方法未调用super().run()")
            
        # 检查是否有额外的监控任务
        if "_monitor_data_flow" in run_method_content:
            issues.append(f"{exchange}: run()方法中启动了额外的监控任务，可能导致并发冲突")
            
        return {"issues": issues, "content_length": len(run_method_content)}
        
    def _check_monitoring_tasks(self, content: str, exchange: str) -> Dict[str, Any]:
        """检查监控任务"""
        issues = []
        
        # 检查是否存在_monitor_data_flow方法
        if "_monitor_data_flow" in content:
            issues.append(f"{exchange}: 存在_monitor_data_flow方法，可能与主消息循环产生并发冲突")
            
        # 检查是否存在_handle_data_flow_blocking方法
        if "_handle_data_flow_blocking" in content:
            issues.append(f"{exchange}: 存在_handle_data_flow_blocking方法，可能触发额外WebSocket操作")
            
        return {"issues": issues}
        
    def _check_subscription_concurrency(self, content: str, exchange: str) -> Dict[str, Any]:
        """检查订阅机制中的并发处理"""
        issues = []
        
        # 检查是否使用了asyncio.gather
        if "asyncio.gather" in content:
            issues.append(f"{exchange}: 订阅机制使用asyncio.gather，可能导致并发冲突")
            
        # 检查批次订阅实现
        if "_send_subscription_batch" in content:
            # 检查是否是顺序执行
            batch_pattern = r'await self\._send_subscription_batch'
            batch_matches = re.findall(batch_pattern, content)
            if len(batch_matches) > 0:
                # 检查是否在循环中顺序执行
                if "for" in content and "await self._send_subscription_batch" in content:
                    # 这是好的，顺序执行
                    pass
                else:
                    issues.append(f"{exchange}: 批次订阅可能存在并发执行")
                    
        return {"issues": issues}
        
    def _check_health_monitoring(self, content: str, exchange: str) -> Dict[str, Any]:
        """检查健康监控配置"""
        issues = []
        
        # 检查是否禁用了健康监控
        if "auto_recovery_enabled = False" in content:
            return {"issues": [], "status": "DISABLED", "reason": "避免并发冲突"}
            
        # 检查是否有健康监控相关配置
        if "_integrated_with_pool = False" in content:
            return {"issues": [], "status": "DISABLED", "reason": "禁用连接池集成"}
            
        return {"issues": issues, "status": "ENABLED"}
        
    def _check_heartbeat_configuration(self, content: str, exchange: str) -> Dict[str, Any]:
        """检查心跳间隔配置"""
        issues = []
        
        # 查找心跳间隔配置
        heartbeat_pattern = r'self\.heartbeat_interval\s*=\s*(\d+)'
        heartbeat_matches = re.findall(heartbeat_pattern, content)
        
        if heartbeat_matches:
            intervals = [int(match) for match in heartbeat_matches]
            if len(set(intervals)) > 1:
                issues.append(f"{exchange}: 心跳间隔配置不一致: {intervals}")
            return {"issues": issues, "intervals": intervals}
        else:
            issues.append(f"{exchange}: 未找到心跳间隔配置")
            return {"issues": issues}
            
    def _check_message_throttling(self, content: str, exchange: str) -> Dict[str, Any]:
        """检查消息处理限流"""
        issues = []
        
        # 检查是否有消息限流逻辑
        if "last_orderbook_time" in content and "< 0.1" in content:
            issues.append(f"{exchange}: 存在100ms消息限流，可能导致重要数据丢失")
            
        return {"issues": issues}
        
    def _check_architecture_simplicity(self, content: str, exchange: str) -> Dict[str, Any]:
        """检查架构简洁性"""
        score = 100
        issues = []
        
        # 检查run()方法长度
        run_method_pattern = r'async def run\(self\):(.*?)(?=async def|\Z)'
        run_match = re.search(run_method_pattern, content, re.DOTALL)
        
        if run_match:
            run_method_lines = len(run_match.group(1).split('\n'))
            if run_method_lines > 20:
                score -= 10
                issues.append(f"{exchange}: run()方法过长({run_method_lines}行)，不够简洁")
                
        # 检查是否有不必要的监控任务
        if "_monitor_data_flow" in content:
            score -= 20
            issues.append(f"{exchange}: 存在额外监控任务，违反简洁原则")
            
        # 检查是否有复杂的并发处理
        if "asyncio.gather" in content:
            score -= 15
            issues.append(f"{exchange}: 使用复杂并发处理，不够简洁")
            
        return {"score": score, "issues": issues}
        
    def _check_api_compliance(self, content: str, exchange: str) -> Dict[str, Any]:
        """检查API规范符合度"""
        compliance_score = 100
        issues = []
        
        # 检查是否遵循官方API规范
        if "官方API规范" in content or "官方SDK" in content:
            # 有官方规范注释，加分
            pass
        else:
            compliance_score -= 10
            issues.append(f"{exchange}: 缺少官方API规范注释")
            
        return {"score": compliance_score, "issues": issues}
        
    def _check_recv_calls(self, content: str) -> Dict[str, Any]:
        """检查recv()调用"""
        issues = []
        
        # 查找所有recv()调用
        recv_pattern = r'\.recv\(\)'
        recv_matches = re.findall(recv_pattern, content)
        
        if len(recv_matches) > 1:
            issues.append(f"发现{len(recv_matches)}个recv()调用，可能存在并发访问")
            
        # 检查主消息循环中的recv()调用
        main_loop_pattern = r'while self\.running and self\.ws:(.*?)except'
        main_loop_match = re.search(main_loop_pattern, content, re.DOTALL)
        
        if main_loop_match and "recv()" in main_loop_match.group(1):
            # 这是正常的主消息循环
            pass
        else:
            issues.append("未找到主消息循环中的recv()调用")
            
        return {"issues": issues, "recv_count": len(recv_matches)}
        
    def _check_base_health_monitoring(self, content: str) -> Dict[str, Any]:
        """检查基类健康监控"""
        issues = []
        
        # 检查健康监控任务启动逻辑
        if "_active_health_monitoring" in content:
            # 检查是否有条件禁用
            if 'exchange_name.lower() != "okx"' in content:
                return {"issues": [], "status": "CONDITIONAL", "okx_disabled": True}
            else:
                issues.append("健康监控任务可能与主消息循环产生并发冲突")
                
        return {"issues": issues}
        
    def _check_heartbeat_task(self, content: str) -> Dict[str, Any]:
        """检查心跳任务"""
        issues = []
        
        # 检查心跳任务启动
        if "_heartbeat_loop" in content:
            # 检查是否正确启动
            if "asyncio.create_task(self._heartbeat_loop())" in content:
                return {"issues": [], "status": "PROPERLY_CONFIGURED"}
            else:
                issues.append("心跳任务启动方式可能不正确")
                
        return {"issues": issues}
        
    def _check_connection_issue_handling(self, content: str) -> Dict[str, Any]:
        """检查连接问题处理"""
        issues = []
        
        # 检查handle_connection_issue方法
        if "handle_connection_issue" in content:
            return {"issues": [], "status": "AVAILABLE"}
        else:
            issues.append("缺少连接问题处理方法")
            
        return {"issues": issues}
        
    def _generate_diagnosis_report(self):
        """生成诊断报告"""
        print("\n" + "="*80)
        print("🔍 WebSocket架构设计问题诊断报告")
        print("="*80)
        
        # 统计问题
        critical_issues = [issue for issue in self.issues if issue["type"] == "CRITICAL"]
        warning_issues = [issue for issue in self.issues if issue["type"] == "WARNING"]
        
        print(f"\n📊 问题统计:")
        print(f"   🚨 严重问题: {len(critical_issues)}")
        print(f"   ⚠️  警告问题: {len(warning_issues)}")
        
        # 详细分析结果
        print(f"\n📋 详细分析结果:")
        
        for exchange, results in self.analysis_results.items():
            print(f"\n🔸 {exchange.upper()}:")
            for category, data in results.items():
                if isinstance(data, dict) and "issues" in data:
                    if data["issues"]:
                        print(f"   ❌ {category}: {len(data['issues'])} 个问题")
                        for issue in data["issues"]:
                            print(f"      - {issue}")
                    else:
                        print(f"   ✅ {category}: 无问题")
                        
        # 生成修复建议
        self._generate_fix_recommendations()
        
        # 保存诊断结果
        self._save_diagnosis_results()
        
    def _generate_fix_recommendations(self):
        """生成修复建议"""
        print(f"\n🔧 修复建议:")
        
        # 基于分析结果生成建议
        if "okx" in self.analysis_results:
            okx_results = self.analysis_results["okx"]
            if any("监控任务" in str(issue) for result in okx_results.values() 
                   if isinstance(result, dict) and "issues" in result 
                   for issue in result["issues"]):
                print("   1. 🔥 移除OKX WebSocket中的额外监控任务")
                print("      - 删除_monitor_data_flow()方法")
                print("      - 删除_handle_data_flow_blocking()方法")
                print("      - 采用Bybit的简洁架构模式")
                
        if "gate" in self.analysis_results:
            gate_results = self.analysis_results["gate"]
            if any("心跳间隔" in str(issue) for result in gate_results.values() 
                   if isinstance(result, dict) and "issues" in result 
                   for issue in result["issues"]):
                print("   2. 🔥 统一Gate.io心跳间隔配置")
                print("      - 将所有心跳间隔统一为20秒")
                
        print("   3. 🔥 遵守WebSocket单一消费者原则")
        print("      - 确保只有主消息循环调用recv()")
        print("      - 禁用可能导致并发冲突的健康监控任务")
        
        print("   4. 🔥 实施统一消息队列机制")
        print("      - 主消息循环负责接收消息")
        print("      - 通过队列分发消息给处理器")
        
    def _save_diagnosis_results(self):
        """保存诊断结果"""
        timestamp = int(time.time())
        filename = f"websocket_architecture_diagnosis_{timestamp}.json"
        filepath = self.project_root / "diagnostic_scripts" / filename
        
        diagnosis_data = {
            "timestamp": timestamp,
            "issues": self.issues,
            "analysis_results": self.analysis_results,
            "summary": {
                "total_issues": len(self.issues),
                "critical_issues": len([i for i in self.issues if i["type"] == "CRITICAL"]),
                "warning_issues": len([i for i in self.issues if i["type"] == "WARNING"])
            }
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(diagnosis_data, f, indent=2, ensure_ascii=False)
            
        print(f"\n💾 诊断结果已保存到: {filename}")

def main():
    """主函数"""
    print("🚀 WebSocket架构设计问题精确诊断")
    print("="*50)
    
    diagnostic = WebSocketArchitectureDiagnostic()
    diagnostic.analyze_websocket_architecture()
    
    print("\n✅ 诊断完成！")

if __name__ == "__main__":
    main()
