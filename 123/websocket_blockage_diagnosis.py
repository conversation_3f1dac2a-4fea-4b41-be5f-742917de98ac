#!/usr/bin/env python3
"""
WebSocket数据阻塞问题精确诊断脚本
诊断OKX、Bybit、Gate三交易所的WebSocket连接问题

问题分析基于2025-08-04日志：
1. OKX: 并发recv()冲突导致数据流阻塞
2. Bybit: 订阅失败 + SHIB-USDT交易对不存在
3. Gate: 大量订阅失败
4. API限流: OKX Too Many Requests (50011)

诊断目标：
- 复现并发访问问题
- 验证交易对格式问题 
- 测试订阅逻辑正确性
- 检测API频率限制合规性
"""

import asyncio
import json
import logging
import time
import websockets
import traceback
from typing import List, Dict, Any
from datetime import datetime
import sys
import os

# 添加项目路径
sys.path.append('/root/myproject/123/70 gate和okx还是数据阻塞/123')

# 配置诊断日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [DIAG] %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/websocket_diagnosis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('WebSocketDiagnosis')

class WebSocketDiagnosisTest:
    """WebSocket问题诊断测试套件"""
    
    def __init__(self):
        self.test_symbols = ["BTC-USDT", "ETH-USDT", "SOL-USDT", "DOGE-USDT", "SHIB-USDT"]
        self.results = {
            "okx": {"concurrent_access": False, "api_rate_limit": False, "connection_stable": False},
            "bybit": {"subscription_success": False, "symbol_validation": False, "connection_stable": False},
            "gate": {"subscription_success": False, "connection_stable": False},
            "summary": {"total_tests": 0, "passed": 0, "failed": 0}
        }
        
    async def run_full_diagnosis(self):
        """运行完整诊断流程"""
        logger.info("🔍 开始WebSocket数据阻塞问题诊断")
        logger.info(f"📊 测试交易对: {', '.join(self.test_symbols)}")
        
        # 1. OKX并发访问诊断
        await self._diagnose_okx_concurrent_access()
        
        # 2. Bybit订阅问题诊断
        await self._diagnose_bybit_subscription()
        
        # 3. Gate订阅问题诊断
        await self._diagnose_gate_subscription()
        
        # 4. 生成诊断报告
        self._generate_diagnosis_report()
    
    async def _diagnose_okx_concurrent_access(self):
        """诊断OKX并发访问问题"""
        logger.info("🔍 [OKX] 开始诊断并发访问问题")
        
        try:
            # 测试1: 模拟并发recv()调用
            concurrent_success = await self._test_okx_concurrent_recv()
            self.results["okx"]["concurrent_access"] = concurrent_success
            
            # 测试2: API频率限制测试
            rate_limit_ok = await self._test_okx_api_rate_limit()
            self.results["okx"]["api_rate_limit"] = rate_limit_ok
            
            # 测试3: 连接稳定性
            connection_stable = await self._test_okx_connection_stability()
            self.results["okx"]["connection_stable"] = connection_stable
            
        except Exception as e:
            logger.error(f"❌ [OKX] 诊断异常: {e}")
            logger.error(traceback.format_exc())
    
    async def _test_okx_concurrent_recv(self):
        """测试OKX并发recv()调用问题"""
        logger.info("[OKX] 测试并发recv()调用")
        
        try:
            uri = "wss://ws.okx.com:8443/ws/v5/public"
            
            async with websockets.connect(uri) as websocket:
                # 订阅测试数据
                subscribe_msg = {
                    "op": "subscribe",
                    "args": [{"channel": "books", "instId": "BTC-USDT"}]
                }
                await websocket.send(json.dumps(subscribe_msg))
                
                # 等待订阅确认
                confirm_msg = await websocket.recv()
                logger.info(f"[OKX] 订阅确认: {confirm_msg}")
                
                # 模拟并发recv()调用 - 这应该会触发错误
                async def concurrent_receiver(name):
                    try:
                        for i in range(3):
                            msg = await websocket.recv()
                            logger.info(f"[OKX] {name} 接收消息 {i+1}: {msg[:100]}...")
                            await asyncio.sleep(0.1)
                    except Exception as e:
                        logger.error(f"[OKX] {name} 并发接收异常: {e}")
                        return str(e)
                    return "success"
                
                # 启动多个并发接收器
                tasks = [
                    asyncio.create_task(concurrent_receiver("接收器1")),
                    asyncio.create_task(concurrent_receiver("接收器2")),
                    asyncio.create_task(concurrent_receiver("接收器3"))
                ]
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 检查是否出现并发错误
                concurrent_errors = [r for r in results if isinstance(r, str) and "recv" in r.lower()]
                if concurrent_errors:
                    logger.error(f"❌ [OKX] 检测到并发recv()冲突: {concurrent_errors}")
                    return False
                else:
                    logger.info("✅ [OKX] 未检测到并发访问问题")
                    return True
                    
        except Exception as e:
            logger.error(f"❌ [OKX] 并发测试异常: {e}")
            return False
    
    async def _test_okx_api_rate_limit(self):
        """测试OKX API频率限制"""
        logger.info("[OKX] 测试API频率限制")
        
        try:
            # 模拟快速连续WebSocket连接（可能触发限制）
            connection_count = 0
            max_connections = 5
            
            for i in range(max_connections):
                try:
                    uri = "wss://ws.okx.com:8443/ws/v5/public"
                    websocket = await asyncio.wait_for(
                        websockets.connect(uri), 
                        timeout=5
                    )
                    await websocket.close()
                    connection_count += 1
                    logger.info(f"[OKX] 连接测试 {i+1}/{max_connections} 成功")
                    
                    # 短暂间隔
                    await asyncio.sleep(0.2)
                    
                except asyncio.TimeoutError:
                    logger.warning(f"[OKX] 连接 {i+1} 超时，可能遇到限流")
                    break
                except Exception as e:
                    logger.warning(f"[OKX] 连接 {i+1} 失败: {e}")
                    if "Too Many Requests" in str(e) or "50011" in str(e):
                        logger.error("❌ [OKX] 检测到API限流问题")
                        return False
            
            if connection_count >= max_connections * 0.8:  # 80%成功率认为正常
                logger.info("✅ [OKX] API频率限制测试通过")
                return True
            else:
                logger.warning(f"⚠️ [OKX] 连接成功率较低: {connection_count}/{max_connections}")
                return False
                
        except Exception as e:
            logger.error(f"❌ [OKX] API限制测试异常: {e}")
            return False
    
    async def _test_okx_connection_stability(self):
        """测试OKX连接稳定性"""
        logger.info("[OKX] 测试连接稳定性")
        
        try:
            uri = "wss://ws.okx.com:8443/ws/v5/public"
            
            async with websockets.connect(uri) as websocket:
                # 订阅多个交易对
                for symbol in self.test_symbols[:3]:  # 测试前3个
                    subscribe_msg = {
                        "op": "subscribe",
                        "args": [{"channel": "books", "instId": symbol}]
                    }
                    await websocket.send(json.dumps(subscribe_msg))
                    await asyncio.sleep(0.5)  # 避免过快订阅
                
                # 接收数据30秒，检查是否稳定
                message_count = 0
                start_time = time.time()
                test_duration = 30
                
                while time.time() - start_time < test_duration:
                    try:
                        msg = await asyncio.wait_for(websocket.recv(), timeout=5)
                        message_count += 1
                        
                        if message_count % 10 == 0:
                            logger.info(f"[OKX] 已接收 {message_count} 条消息")
                            
                    except asyncio.TimeoutError:
                        logger.warning("[OKX] 5秒内未接收到消息")
                        break
                
                if message_count > 10:  # 30秒内至少10条消息认为正常
                    logger.info(f"✅ [OKX] 连接稳定，30秒内接收 {message_count} 条消息")
                    return True
                else:
                    logger.error(f"❌ [OKX] 连接不稳定，30秒内仅接收 {message_count} 条消息")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ [OKX] 连接稳定性测试异常: {e}")
            return False
    
    async def _diagnose_bybit_subscription(self):
        """诊断Bybit订阅问题"""
        logger.info("🔍 [Bybit] 开始诊断订阅问题")
        
        try:
            # 测试1: 交易对格式验证
            symbol_valid = await self._test_bybit_symbol_formats()
            self.results["bybit"]["symbol_validation"] = symbol_valid
            
            # 测试2: 订阅成功率
            subscription_success = await self._test_bybit_subscription_success()
            self.results["bybit"]["subscription_success"] = subscription_success
            
            # 测试3: 连接稳定性
            connection_stable = await self._test_bybit_connection_stability()
            self.results["bybit"]["connection_stable"] = connection_stable
            
        except Exception as e:
            logger.error(f"❌ [Bybit] 诊断异常: {e}")
            logger.error(traceback.format_exc())
    
    async def _test_bybit_symbol_formats(self):
        """测试Bybit交易对格式"""
        logger.info("[Bybit] 测试交易对格式")
        
        # 日志显示SHIB-USDT有问题，测试不同格式
        symbol_formats = {
            "SHIB-USDT": "SHIBUSDT",  # 从日志看应该是SHIBUSDT格式
            "BTC-USDT": "BTCUSDT",
            "ETH-USDT": "ETHUSDT"
        }
        
        valid_symbols = []
        invalid_symbols = []
        
        try:
            uri = "wss://stream.bybit.com/v5/public/linear"
            
            async with websockets.connect(uri) as websocket:
                for original, bybit_format in symbol_formats.items():
                    try:
                        # 测试订阅期货合约
                        subscribe_msg = {
                            "op": "subscribe",
                            "args": [f"orderbook.1.{bybit_format}"]
                        }
                        await websocket.send(json.dumps(subscribe_msg))
                        
                        # 等待响应
                        response = await asyncio.wait_for(websocket.recv(), timeout=3)
                        response_data = json.loads(response)
                        
                        if response_data.get("success") is True:
                            valid_symbols.append(bybit_format)
                            logger.info(f"✅ [Bybit] {original} -> {bybit_format} 格式有效")
                        else:
                            invalid_symbols.append(bybit_format)
                            logger.warning(f"⚠️ [Bybit] {original} -> {bybit_format} 订阅失败: {response}")
                            
                    except Exception as e:
                        invalid_symbols.append(bybit_format)
                        logger.error(f"❌ [Bybit] {original} -> {bybit_format} 测试异常: {e}")
                
                # 检查SHIB是否真的无效
                if "SHIBUSDT" in invalid_symbols:
                    logger.error("❌ [Bybit] 确认SHIB-USDT交易对不存在或格式错误")
                    return False
                else:
                    logger.info("✅ [Bybit] 交易对格式验证通过")
                    return True
                    
        except Exception as e:
            logger.error(f"❌ [Bybit] 交易对格式测试异常: {e}")
            return False
    
    async def _test_bybit_subscription_success(self):
        """测试Bybit订阅成功率"""
        logger.info("[Bybit] 测试订阅成功率")
        
        try:
            uri = "wss://stream.bybit.com/v5/public/linear"
            
            async with websockets.connect(uri) as websocket:
                successful_subscriptions = 0
                total_attempts = 0
                
                # 测试有效的交易对（排除SHIB）
                test_symbols = ["BTCUSDT", "ETHUSDT", "SOLUSDT", "DOGEUSDT"]
                
                for symbol in test_symbols:
                    total_attempts += 1
                    try:
                        subscribe_msg = {
                            "op": "subscribe", 
                            "args": [f"orderbook.1.{symbol}"]
                        }
                        await websocket.send(json.dumps(subscribe_msg))
                        
                        response = await asyncio.wait_for(websocket.recv(), timeout=3)
                        response_data = json.loads(response)
                        
                        if response_data.get("success") is True:
                            successful_subscriptions += 1
                            logger.info(f"✅ [Bybit] {symbol} 订阅成功")
                        else:
                            logger.warning(f"⚠️ [Bybit] {symbol} 订阅失败: {response}")
                            
                    except Exception as e:
                        logger.error(f"❌ [Bybit] {symbol} 订阅异常: {e}")
                
                success_rate = successful_subscriptions / total_attempts if total_attempts > 0 else 0
                logger.info(f"[Bybit] 订阅成功率: {successful_subscriptions}/{total_attempts} ({success_rate:.1%})")
                
                return success_rate >= 0.8  # 80%成功率认为正常
                
        except Exception as e:
            logger.error(f"❌ [Bybit] 订阅测试异常: {e}")
            return False
    
    async def _test_bybit_connection_stability(self):
        """测试Bybit连接稳定性"""
        logger.info("[Bybit] 测试连接稳定性")
        
        try:
            uri = "wss://stream.bybit.com/v5/public/linear"
            
            async with websockets.connect(uri) as websocket:
                # 订阅一个有效交易对
                subscribe_msg = {
                    "op": "subscribe",
                    "args": ["orderbook.1.BTCUSDT"]
                }
                await websocket.send(json.dumps(subscribe_msg))
                
                # 接收数据30秒
                message_count = 0
                start_time = time.time()
                test_duration = 30
                
                while time.time() - start_time < test_duration:
                    try:
                        msg = await asyncio.wait_for(websocket.recv(), timeout=5)
                        message_count += 1
                        
                        if message_count % 5 == 0:
                            logger.info(f"[Bybit] 已接收 {message_count} 条消息")
                            
                    except asyncio.TimeoutError:
                        logger.warning("[Bybit] 5秒内未接收到消息")
                        break
                
                if message_count > 5:  # 30秒内至少5条消息认为正常
                    logger.info(f"✅ [Bybit] 连接稳定，30秒内接收 {message_count} 条消息")
                    return True
                else:
                    logger.error(f"❌ [Bybit] 连接不稳定，30秒内仅接收 {message_count} 条消息")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ [Bybit] 连接稳定性测试异常: {e}")
            return False
    
    async def _diagnose_gate_subscription(self):
        """诊断Gate订阅问题"""
        logger.info("🔍 [Gate] 开始诊断订阅问题")
        
        try:
            # 测试1: 订阅成功率
            subscription_success = await self._test_gate_subscription_success()
            self.results["gate"]["subscription_success"] = subscription_success
            
            # 测试2: 连接稳定性
            connection_stable = await self._test_gate_connection_stability()
            self.results["gate"]["connection_stable"] = connection_stable
            
        except Exception as e:
            logger.error(f"❌ [Gate] 诊断异常: {e}")
            logger.error(traceback.format_exc())
    
    async def _test_gate_subscription_success(self):
        """测试Gate订阅成功率"""
        logger.info("[Gate] 测试订阅成功率")
        
        try:
            uri = "wss://api.gateio.ws/ws/v4/"
            
            async with websockets.connect(uri) as websocket:
                successful_subscriptions = 0
                total_attempts = 0
                
                # Gate.io使用下划线格式
                test_symbols = ["BTC_USDT", "ETH_USDT", "SOL_USDT", "DOGE_USDT"]
                
                for symbol in test_symbols:
                    total_attempts += 1
                    try:
                        subscribe_msg = {
                            "time": int(time.time()),
                            "channel": "spot.order_book",
                            "event": "subscribe",
                            "payload": [symbol, "20", "100ms"]
                        }
                        await websocket.send(json.dumps(subscribe_msg))
                        
                        # 等待订阅确认
                        response = await asyncio.wait_for(websocket.recv(), timeout=3)
                        response_data = json.loads(response)
                        
                        if response_data.get("event") == "subscribe" and response_data.get("result", {}).get("status") == "success":
                            successful_subscriptions += 1
                            logger.info(f"✅ [Gate] {symbol} 订阅成功")
                        else:
                            logger.warning(f"⚠️ [Gate] {symbol} 订阅失败: {response}")
                            
                    except Exception as e:
                        logger.error(f"❌ [Gate] {symbol} 订阅异常: {e}")
                
                success_rate = successful_subscriptions / total_attempts if total_attempts > 0 else 0
                logger.info(f"[Gate] 订阅成功率: {successful_subscriptions}/{total_attempts} ({success_rate:.1%})")
                
                return success_rate >= 0.8  # 80%成功率认为正常
                
        except Exception as e:
            logger.error(f"❌ [Gate] 订阅测试异常: {e}")
            return False
    
    async def _test_gate_connection_stability(self):
        """测试Gate连接稳定性"""
        logger.info("[Gate] 测试连接稳定性")
        
        try:
            uri = "wss://api.gateio.ws/ws/v4/"
            
            async with websockets.connect(uri) as websocket:
                # 订阅一个测试交易对
                subscribe_msg = {
                    "time": int(time.time()),
                    "channel": "spot.order_book", 
                    "event": "subscribe",
                    "payload": ["BTC_USDT", "20", "100ms"]
                }
                await websocket.send(json.dumps(subscribe_msg))
                
                # 接收数据30秒
                message_count = 0
                start_time = time.time()
                test_duration = 30
                
                while time.time() - start_time < test_duration:
                    try:
                        msg = await asyncio.wait_for(websocket.recv(), timeout=5)
                        message_count += 1
                        
                        if message_count % 5 == 0:
                            logger.info(f"[Gate] 已接收 {message_count} 条消息")
                            
                    except asyncio.TimeoutError:
                        logger.warning("[Gate] 5秒内未接收到消息")
                        break
                
                if message_count > 5:  # 30秒内至少5条消息认为正常
                    logger.info(f"✅ [Gate] 连接稳定，30秒内接收 {message_count} 条消息")
                    return True
                else:
                    logger.error(f"❌ [Gate] 连接不稳定，30秒内仅接收 {message_count} 条消息")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ [Gate] 连接稳定性测试异常: {e}")
            return False
    
    def _generate_diagnosis_report(self):
        """生成诊断报告"""
        logger.info("📊 生成诊断报告")
        
        total_tests = 0
        passed_tests = 0
        
        report = [
            "=" * 60,
            "WebSocket数据阻塞问题诊断报告",
            "=" * 60,
            "",
            "🔍 OKX诊断结果:",
            f"  - 并发访问测试: {'✅ 通过' if self.results['okx']['concurrent_access'] else '❌ 失败'}",
            f"  - API频率限制: {'✅ 正常' if self.results['okx']['api_rate_limit'] else '❌ 异常'}",
            f"  - 连接稳定性: {'✅ 稳定' if self.results['okx']['connection_stable'] else '❌ 不稳定'}",
            "",
            "🔍 Bybit诊断结果:",
            f"  - 交易对格式验证: {'✅ 有效' if self.results['bybit']['symbol_validation'] else '❌ 无效'}",
            f"  - 订阅成功率: {'✅ 正常' if self.results['bybit']['subscription_success'] else '❌ 异常'}",
            f"  - 连接稳定性: {'✅ 稳定' if self.results['bybit']['connection_stable'] else '❌ 不稳定'}",
            "",
            "🔍 Gate诊断结果:",
            f"  - 订阅成功率: {'✅ 正常' if self.results['gate']['subscription_success'] else '❌ 异常'}",
            f"  - 连接稳定性: {'✅ 稳定' if self.results['gate']['connection_stable'] else '❌ 不稳定'}",
            "",
        ]
        
        # 统计结果
        for exchange in ["okx", "bybit", "gate"]:
            for test, result in self.results[exchange].items():
                total_tests += 1
                if result:
                    passed_tests += 1
        
        self.results["summary"]["total_tests"] = total_tests
        self.results["summary"]["passed"] = passed_tests
        self.results["summary"]["failed"] = total_tests - passed_tests
        
        report.extend([
            "📈 总体结果:",
            f"  - 总测试数: {total_tests}",
            f"  - 通过: {passed_tests}",
            f"  - 失败: {total_tests - passed_tests}",
            f"  - 成功率: {passed_tests/total_tests:.1%}" if total_tests > 0 else "  - 成功率: 0%",
            "",
            "🎯 关键问题识别:",
        ])
        
        # 添加问题分析
        if not self.results["okx"]["concurrent_access"]:
            report.append("  ❌ OKX存在并发recv()冲突问题 - 需要实施单一消费者模式")
        if not self.results["bybit"]["symbol_validation"]:
            report.append("  ❌ Bybit交易对格式错误 - SHIB-USDT应为SHIBUSDT格式")  
        if not self.results["gate"]["subscription_success"]:
            report.append("  ❌ Gate订阅失败率高 - 需要检查订阅消息格式")
        
        report.extend([
            "",
            "💡 修复建议:",
            "  1. OKX: 实施单一消息循环 + 消息队列架构",
            "  2. Bybit: 修正交易对格式映射，过滤无效交易对",
            "  3. Gate: 检查订阅消息格式，增加重试机制",
            "  4. 统一: 实施连接池管理，避免API限流",
            "",
            "=" * 60
        ])
        
        report_text = "\n".join(report)
        logger.info(f"\n{report_text}")
        
        # 保存报告文件
        report_file = f"logs/websocket_diagnosis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_text)
            logger.info(f"📄 诊断报告已保存: {report_file}")
        except Exception as e:
            logger.error(f"保存报告失败: {e}")

async def main():
    """主函数"""
    # 确保logs目录存在
    os.makedirs('logs', exist_ok=True)
    
    # 运行诊断
    diagnosis = WebSocketDiagnosisTest()
    await diagnosis.run_full_diagnosis()

if __name__ == "__main__":
    asyncio.run(main())