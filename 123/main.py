#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期现套利自动化系统 - 主程序入口
多交易所期货溢价对冲套利（完美对冲版）
支持Gate、Bybit、OKX三个交易所
"""

import sys
import os
import signal
import logging
import asyncio
import argparse
from typing import Optional, Dict, Any
from dotenv import load_dotenv
import time

# 添加项目路径到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 🚀 自动应用性能优化 - 无需单独运行
from utils.performance_optimizer import ensure_optimized
ensure_optimized(silent=True)

# 🔥 初始化统一日志系统 - 8个指定日志文件
from utils.log_setup import setup_logging
setup_logging()

# 获取主程序日志器
logger = logging.getLogger("main")

# 🔥 修复：只禁用DEBUG刷屏，保留重要日志
import logging
import sys

# 只移除控制台处理器，保留文件日志处理器
for handler in logging.getLogger().handlers[:]:
    if isinstance(handler, logging.StreamHandler) and not isinstance(handler, logging.FileHandler):
        logging.getLogger().removeHandler(handler)

# 设置根日志器级别为INFO，允许重要日志但禁用DEBUG
logging.getLogger().setLevel(logging.INFO)

# 导入配置
from config.settings import Settings

# 导入核心模块
from core.arbitrage_engine import ArbitrageEngine

# 导入WebSocket模块
from websocket import stop_all_websockets

# 🔥 VPS模式：删除所有显示模块，专注于交易执行
# 创建空的兼容性函数
def update_system_status(status: str):
    """兼容性函数 - VPS模式下为空实现"""
    pass

def update_websocket_status(status: str):
    """兼容性函数 - VPS模式下为空实现"""
    pass

def update_execution_status(status: str):
    """兼容性函数 - VPS模式下为空实现"""
    pass

def update_fund_status(status: str):
    """兼容性函数 - VPS模式下为空实现"""
    pass

def update_exchange_status(exchange: str, status: str, **kwargs):
    """兼容性函数 - VPS模式下为空实现"""
    pass

def start_simple_display():
    """启动简单的WebSocket价格日志显示 - VPS模式"""
    import threading
    import time

    def tail_websocket_prices():
        """实时显示websocket_prices日志内容 - 只显示价格+深度+差价"""
        # 🔥 修复：使用与log_setup.py一致的文件名（不带时间戳）
        log_file = "logs/websocket_prices.log"

        # 🔥 修复重复显示：清屏但不重复打印标题，让日志文件的头部自然显示
        os.system('cls' if os.name == 'nt' else 'clear')

        last_position = 0

        while True:
            try:
                if os.path.exists(log_file):
                    with open(log_file, 'r', encoding='utf-8') as f:
                        f.seek(last_position)
                        new_content = f.read()

                        if new_content:
                            last_position = f.tell()
                            lines = new_content.strip().split('\n')

                            for line in lines:
                                # 🔥 修复：显示所有日志内容，包括头部信息和价格数据
                                if line.strip():
                                    # 🔥 修复：正确提取时间戳（日志格式：21:52:27 🚀 [A] BTC-USDT...）
                                    if ' ' in line and ':' in line[:10] and not line.startswith('=') and not line.startswith('📊') and not line.startswith('🎯') and not line.startswith('🚀 实时监控格式'):
                                        # 时间戳在行首，格式：HH:MM:SS
                                        parts = line.split(' ', 1)
                                        if len(parts) >= 2 and ':' in parts[0]:
                                            log_time = parts[0]  # 21:52:27
                                            clean_line = parts[1]  # 🚀 [A] BTC-USDT...
                                            print(f"[{log_time}] {clean_line}")
                                        else:
                                            print(line)
                                    else:
                                        # 直接显示头部信息和其他格式化内容
                                        print(line)

                time.sleep(0.5)  # 每0.5秒检查一次

            except Exception as e:
                time.sleep(1)  # 出错时等待1秒

    # 启动后台线程显示日志
    display_thread = threading.Thread(target=tail_websocket_prices, daemon=True)
    display_thread.start()

def stop_simple_display():
    """停止简单显示 - VPS模式下为空实现"""
    pass

# 导入资金管理器
from fund_management.fund_manager import FundManager
# 🔥 交易所导入已移至统一初始化模块

# 加载环境变量
load_dotenv()

# 🔥 加载SSL配置环境变量
load_dotenv('.env.ssl')

# 获取日志器
logger = logging.getLogger("main")


class ArbitrageSystem:
    """期现套利系统主控制类"""

    def __init__(self):
        self.config = Settings()
        self.engine: Optional[ArbitrageEngine] = None
        self.running = False
        self.ws_task = None
        self._last_price_log = {}  # 价格变化记录
        self._last_arbitrage_log = {}  # 套利机会记录，避免重复输出
        self._arbitrage_log_interval = 10  # 套利机会日志间隔（秒）
        self._spot_premium_log = {}  # 现货溢价日志记录，避免刷屏
        self._spot_premium_log_interval = 60  # 现货溢价日志间隔（60秒）

        # 添加缺失的属性
        self.logger = logging.getLogger("arbitrage_system")

        # 🔥 重要：显示系统将在引擎初始化后创建，确保数据源统一
        self.display = None

        # 初始化交易所和资金管理器
        self.exchanges = {}
        self.fund_manager = None

        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        # 静默初始化，只记录到日志
        self.logger.info("🔥 期现套利系统初始化 - 统一数据源版本")

        # 系统状态
        self.running = False
        self.ws_task = None
        
        # 🔥 修复：初始化价格日志字典
        self._last_price_log = {}

        # 核心组件
        self.exchanges = {}
        self.fund_manager = None
        self.engine = None

        # 获取日志器（不重复配置，使用已设置的日志系统）
        self.logger = logging.getLogger('ArbitrageSystem')

    def _signal_handler(self, signum, frame):
        """处理系统信号，优雅关闭 - 🔥 修复：正确处理异步shutdown"""
        logger.info(f"接收到信号 {signum}，开始优雅关闭系统...")

        # 🔥 关键修复：不能在信号处理器中直接调用异步方法
        # 设置标志让主循环处理关闭
        self.running = False

        # 🔥 如果当前在事件循环中，创建关闭任务
        try:
            loop = asyncio.get_running_loop()
            if loop and not loop.is_closed():
                # 在事件循环中创建关闭任务
                loop.create_task(self.shutdown())
        except RuntimeError:
            # 没有运行的事件循环，只设置标志
            logger.info("没有运行的事件循环，设置关闭标志")

    def _display_startup_banner(self):
        """显示启动横幅 - VPS模式，只记录日志"""
        # VPS模式：只记录日志，不显示界面
        logger.info("⚙️ 套利系统 - 初始化中")
        logger.info("🔌 准备连接WebSocket")
        logger.info("🔄 系统启动中")

    async def initialize(self) -> bool:
        """初始化系统 - 静默模式"""
        try:
            # 🔥 VPS模式：启动简单显示
            start_simple_display()

            # 显示启动信息
            self._display_startup_banner()

            # 🔥 删除重复初始化：使用统一的交易系统初始化器
            await self._initialize_unified_system()

            return True
        except Exception as e:
            logger.error(f"❌ 系统初始化异常: {type(e).__name__}: {str(e)}", exc_info=True)
            return False

    async def _initialize_unified_system(self):
        """🔥 统一系统初始化：使用修复后的TradingSystemInitializer"""
        try:
            logger.info("🚀 使用修复后的统一初始化模块启动系统...")

            # 🔥 完美修复：完全使用统一初始化模块，移除所有重复代码
            from core.trading_system_initializer import get_trading_system_initializer
            initializer = get_trading_system_initializer()

            # 🔥 唯一步骤：使用统一初始化模块完成所有初始化
            logger.info("🔥 使用统一初始化模块完成所有系统初始化...")
            success = await initializer.initialize_all_systems()

            if not success:
                logger.error("❌ 统一初始化模块失败")
                return False

            logger.info("🔥 开始获取初始化结果...")

            # 🔥 获取初始化结果
            try:
                self.exchanges = initializer.exchanges if hasattr(initializer, 'exchanges') else {}
                logger.info(f"✅ 获取交易所: {len(self.exchanges) if self.exchanges else 0}个")

                self.fund_manager = initializer.fund_manager if hasattr(initializer, 'fund_manager') else None
                logger.info(f"✅ 获取资金管理器: {'已初始化' if self.fund_manager else '未初始化'}")

                self.engine = initializer.engine if hasattr(initializer, 'engine') else None
                logger.info(f"✅ 获取套利引擎: {'已初始化' if self.engine else '未初始化'}")

            except Exception as e:
                logger.error(f"❌ 获取初始化结果失败: {e}")
                return False

            logger.info("✅ 统一初始化模块完成，所有系统就绪")

            # 🔥 启动监控系统（唯一保留的额外步骤）
            logger.info("🔥 启动监控系统...")
            await self._start_monitoring()
            logger.info("✅ 监控系统启动成功")

            logger.info("🎉 所有系统初始化完成！")
            return True

        except Exception as e:
            logger.error(f"❌ 统一系统初始化异常: {e}", exc_info=True)
            return False

    # 🔥 完美修复：删除所有重复的初始化方法，统一初始化模块已处理

    async def _start_monitoring(self):
        """🔥 完美修复：启动监控系统 - 统一初始化模块已处理所有初始化"""
        try:
            logger.info("📊 启动监控系统...")

            # VPS模式：只记录日志
            logger.info("🟢 系统运行正常")
            logger.info("🔍 监控期货溢价机会")

            logger.info("✅ 监控系统启动完成")

        except Exception as e:
            logger.error(f"❌ 监控系统启动失败: {e}")

    # 🔥 完美修复：删除所有重复的初始化方法，统一初始化模块已处理

    # 🔥 完美修复：删除所有重复的WebSocket初始化代码，统一初始化模块已处理

    async def start(self):
        """启动系统 - 🔥 修复：添加实际运行逻辑"""
        try:
            # 🔥 步骤1：完成系统初始化
            logger.info("🚀 开始系统初始化...")
            if not await self.initialize():
                logger.error("❌ 系统初始化失败")
                return

            # 🔥 步骤2：启动实际业务逻辑
            self.running = True
            logger.info("✅ 系统初始化完成，开始启动业务逻辑...")

            # VPS模式：只记录日志状态
            logger.info("🟢 系统运行正常")
            logger.info("🔍 监控期货溢价机会")

            # 🔥 步骤3：启动数据监控和套利检测
            await self._start_data_monitoring()

            # 🔥 步骤4：主循环 - 等待ArbitrageEngine后台运行
            logger.info("🔍 ArbitrageEngine已在后台运行，主线程等待...")
            while self.running:
                try:
                    # 🔥 修复：不再重复套利检测，ArbitrageEngine已在后台运行
                    # 主线程只需要保持运行状态，让ArbitrageEngine处理所有套利逻辑
                    await asyncio.sleep(1)

                except KeyboardInterrupt:
                    logger.info("用户中断，正在安全关闭...")
                    break
                except Exception as e:
                    logger.error(f"运行异常: {str(e)}", exc_info=True)
                    await asyncio.sleep(5)  # 错误后等待5秒

        except Exception as e:
            logger.error(f"系统启动异常: {str(e)}", exc_info=True)
        finally:
            await self.shutdown()

    async def _start_data_monitoring(self):
        """🔥 新增：启动数据监控"""
        try:
            logger.info("📊 启动数据监控系统...")
            
            # 启动WebSocket连接
            if hasattr(self, 'ws_task') and not self.ws_task:
                logger.info("🔌 启动WebSocket数据流...")
                # WebSocket在_initialize_websockets中已经配置，这里只需确认启动
                
            # 启动余额监控
            if self.fund_manager:
                logger.info("💰 启动余额监控...")
                # 资金管理器已在初始化中配置
                
            # 确认套利引擎状态
            if self.engine:
                logger.info("⚡ 套利引擎已就绪")
            else:
                logger.warning("⚠️ 套利引擎未就绪")
                
            logger.info("✅ 数据监控系统启动完成")
            
        except Exception as e:
            logger.error(f"❌ 数据监控启动失败: {e}")

    async def _monitor_arbitrage_opportunities(self):
        """🔥 删除重复套利监控：统一使用ArbitrageEngine的套利检测"""
        # 🔥 重要：不再在main.py中重复套利检测
        # 所有套利检测统一由ArbitrageEngine管理，避免重复和冲突
        pass

    async def shutdown(self):
        """关闭系统 - 静默模式"""
        if not self.running and not self.ws_task:
            return

        self.running = False

        # VPS模式：只记录关闭状态
        logger.info("🛑 系统关闭中...")
        logger.info("🔌 断开WebSocket连接中")
        logger.info("⏹️ 停止执行")

        try:
            # 关闭WebSocket连接
            if self.ws_task:
                stop_all_websockets()
                try:
                    await asyncio.wait_for(self.ws_task, timeout=5)
                except asyncio.TimeoutError:
                    pass
                finally:
                    self.ws_task = None

            # 清理资金管理器
            if self.fund_manager:
                try:
                    await self.fund_manager.cleanup()
                except Exception as e:
                    logger.error(f"❌ 资金管理器清理失败: {e}")

            # 关闭交易所连接
            for name, exchange in self.exchanges.items():
                try:
                    await exchange.close()
                except Exception as e:
                    logger.error(f"❌ 关闭{name}交易所连接失败: {e}")

            # 🔥 新增：清理所有HTTP会话 - 解决aiohttp资源泄漏问题
            try:
                from core.unified_http_session_manager import cleanup_all_http_sessions
                await cleanup_all_http_sessions()
                logger.info("✅ 所有HTTP会话已清理完成")
            except Exception as e:
                logger.error(f"❌ HTTP会话清理失败: {e}")

            # 🔥 删除旧精度系统停止调用 - 新预加载器系统无需手动停止
            logger.info("✅ 预加载器系统自动清理完成")

            # VPS模式：关闭简单显示
            stop_simple_display()
            logger.info("✅ 系统清理完成")

        except Exception as e:
            logger.error(f"关闭系统时发生异常: {str(e)}", exc_info=True)

    async def _verify_configuration(self) -> bool:
        """验证关键配置参数"""
        try:
            logger.info("验证系统配置...")

            # 验证交易所API密钥
            required_keys = [
                'GATE_API_KEY', 'GATE_API_SECRET',
                'BYBIT_API_KEY', 'BYBIT_API_SECRET',
                'OKX_API_KEY', 'OKX_API_SECRET', 'OKX_API_PASSPHRASE'
            ]

            missing_keys = []
            for key in required_keys:
                if not os.getenv(key):
                    missing_keys.append(key)

            if missing_keys:
                logger.error(f"缺少必需的API密钥: {', '.join(missing_keys)}")
                return False

            # 🔥 删除重复配置读取：使用配置管理器
            try:
                from config.settings import get_config
                config = get_config()
                min_spread = config.trading.min_spread
                logger.info(f"最小价差阈值: {min_spread:.4f} ({min_spread*100:.2f}%)")

                max_order = config.MAX_ORDER_AMOUNT_USD
                logger.info(f"最大订单金额: ${max_order}")

                # 🔥 删除重复配置读取：使用通用代币系统验证交易对
                from core.universal_token_system import get_universal_token_system
                token_system = get_universal_token_system()
                target_symbols = token_system.get_supported_symbols()
                if not target_symbols:
                    logger.error("TARGET_SYMBOLS配置为空")
                    return False

                # 🔥 新增：系统启动前交易对预验证
                logger.info("🔍 执行系统启动前交易对预验证...")
                validation_result = await token_system.validate_symbols_for_system_startup()

                if not validation_result["valid_symbols"]:
                    logger.error("❌ 没有有效的交易对，无法启动系统")
                    return False

                # 更新支持的交易对列表（移除无效的）
                if validation_result["invalid_symbols"]:
                    logger.warning(f"⚠️ 移除无效交易对: {', '.join(validation_result['invalid_symbols'])}")
                    # 这里不需要更新，因为validate_symbol_for_exchange会自动过滤

                logger.info(f"✅ 预验证通过，有效交易对: {', '.join(validation_result['valid_symbols'])}")
                logger.info("系统将自动转换为各交易所支持的格式并实施优雅降级")

            except (ValueError, TypeError) as e:
                logger.error(f"配置参数格式错误: {e}")
                return False

            logger.info("✅ 配置验证通过")
            return True

        except Exception as e:
            logger.error(f"配置验证失败: {str(e)}")
            return False

    def _standardize_symbol_v3(self, symbol: str, exchange: str = "") -> str:
        """
        标准化交易对格式 V3 - 🔥 新版本：针对性修复Bybit显示问题
        
        Args:
            symbol: 原始交易对格式
            exchange: 交易所名称，用于优化转换逻辑
            
        Returns:
            str: 标准化的交易对格式 (BTC-USDT)
        """
        if not symbol:
            return ""

        original_symbol = symbol
        
        try:
            # 🔥 针对Bybit的特殊处理
            if exchange.lower() == 'bybit':
                # 🚨 关键修复：检查是否已经是标准格式
                if '-' in symbol and symbol.endswith('-USDT'):
                    # 已经是标准格式BTC-USDT，直接返回
                    return symbol
                elif symbol.endswith('USDT') and len(symbol) > 4 and '-' not in symbol:
                    # Bybit原始格式: BTCUSDT -> BTC-USDT
                    base = symbol[:-4]
                    result = f"{base}-USDT"
                    self.logger.debug(f"🎯 Bybit转换V3: {original_symbol} -> {result}")
                    return result
                else:
                    # 其他格式，直接返回
                    return symbol
                    
            # 其他交易所的处理
            elif exchange.lower() == 'gate':
                # Gate格式: BTC_USDT -> BTC-USDT
                if '_' in symbol:
                    result = symbol.replace('_', '-')
                    return result
                else:
                    return symbol
                    
            elif exchange.lower() == 'okx':
                # OKX格式: 处理SWAP后缀
                if symbol.endswith('-SWAP'):
                    # BTC-USDT-SWAP -> BTC-USDT
                    result = symbol.replace('-SWAP', '')
                    return result
                elif '-' in symbol:
                    # BTC-USDT -> BTC-USDT (保持不变)
                    return symbol
                else:
                    return symbol
                    
            else:
                # 未知交易所，使用通用逻辑
                if '_' in symbol:
                    return symbol.replace('_', '-')
                elif symbol.endswith('USDT') and len(symbol) > 4:
                    base = symbol[:-4]
                    return f"{base}-USDT"
                else:
                    return symbol
                    
        except Exception as e:
            self.logger.error(f"❌ Symbol标准化V3异常: {original_symbol} -> 错误: {e}")
            return original_symbol

    # 🔥 删除重复套利检测方法：统一使用OpportunityScanner
    # 原_check_arbitrage_opportunity()方法已删除，避免与OpportunityScanner重复
    # 所有套利检测逻辑统一在core/opportunity_scanner.py中实现

    # 🔥 删除重复执行方法：统一使用ArbitrageEngine的执行逻辑
    # 原_execute_arbitrage_opportunity()方法已删除，避免与ArbitrageEngine重复
    # 所有套利执行逻辑统一在core/arbitrage_engine.py中实现

    def _log_arbitrage_opportunity(self, opportunity: Dict[str, Any]):
        """记录套利机会"""
        self.logger.info(
            f"[OPPORTUNITY] Arbitrage [{opportunity['combination']}]: "
            f"{opportunity['symbol']} | "
            f"{opportunity['spot_exchange']}spot=${opportunity['spot_price']:.2f} "
            f"{opportunity['futures_exchange']}futures=${opportunity['futures_price']:.2f} "
            f"spread={opportunity['spread']:.2f}%"
        )


async def run_strategy():
    """运行套利策略"""
    system = ArbitrageSystem()
    await system.start()


async def main_async():
    """异步主函数"""
    try:
        await run_strategy()
    except KeyboardInterrupt:
        logger.warning("用户中断程序")
    except Exception as e:
        logger.error(f"程序异常退出: {str(e)}", exc_info=True)
    finally:
        logger.info("感谢使用期现套利系统")


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="期现套利自动化系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py                    # 运行套利系统
  python main.py --test             # 运行集成测试
  python main.py --test --countdown # 运行集成测试(带倒计时)
        """
    )

    parser.add_argument(
        "--test",
        action="store_true",
        help="运行集成测试而不是启动套利系统"
    )

    parser.add_argument(
        "--countdown",
        action="store_true",
        help="运行测试前显示倒计时"
    )

    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="设置日志级别"
    )

    return parser.parse_args()


async def run_integration_test(show_countdown: bool = False):
    """运行集成测试"""
    try:
        from rich.console import Console
        console = Console()
        
        console.print("[bold blue]🧪 开始运行集成测试...[/bold blue]")

        # 🔥 修复：使用现有的测试脚本替代不存在的模块
        import subprocess
        import sys

        if show_countdown:
            console.print("[yellow]倒计时功能暂未实现，直接运行测试[/yellow]")

        # 🚨 修复：直接运行现有的测试脚本，但不捕获输出，让其直接显示
        console.print("[blue]运行交易所API集成测试...[/blue]")
        
        # 🔥 关键修复：不捕获输出，让测试脚本直接输出到控制台
        result = subprocess.run([sys.executable, "test_exchanges_integration.py"], 
                              cwd=os.getcwd())
        
        if result.returncode == 0:
            console.print("[bold green]✅ 集成测试全部通过![/bold green]")
            return True
        else:
            console.print("[bold red]❌ 集成测试失败![/bold red]")
            return False

    except Exception as e:
        from rich.console import Console
        console = Console()
        console.print(f"[bold red]❌ 测试执行异常: {str(e)}[/bold red]")
        logger.error(f"测试执行异常: {str(e)}", exc_info=True)
        return False


def main():
    """主函数"""
    try:
        # 解析命令行参数
        args = parse_arguments()

        # 设置日志级别
        if args.log_level:
            logging.getLogger().setLevel(getattr(logging, args.log_level))

        # 根据参数选择运行模式
        if args.test:
            # 运行集成测试
            success = asyncio.run(run_integration_test(args.countdown))
            sys.exit(0 if success else 1)
        else:
            # 运行套利系统
            asyncio.run(main_async())

    except KeyboardInterrupt:
        logger.warning("用户中断程序")
        # 🔥 新增：用户中断时也清理HTTP会话
        try:
            from core.unified_http_session_manager import cleanup_all_http_sessions
            asyncio.run(cleanup_all_http_sessions())
            logger.info("✅ 用户中断时HTTP会话已清理")
        except Exception as cleanup_e:
            logger.error(f"❌ 用户中断时HTTP会话清理失败: {cleanup_e}")
    except Exception as e:
        logger.error(f"程序异常退出: {str(e)}", exc_info=True)
        # 🔥 新增：异常退出时也清理HTTP会话
        try:
            from core.unified_http_session_manager import cleanup_all_http_sessions
            asyncio.run(cleanup_all_http_sessions())
            logger.info("✅ 异常退出时HTTP会话已清理")
        except Exception as cleanup_e:
            logger.error(f"❌ 异常退出时HTTP会话清理失败: {cleanup_e}")


if __name__ == '__main__':
    main()