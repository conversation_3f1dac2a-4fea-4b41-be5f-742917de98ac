2025-08-04 12:40:08,606 [INFO] 🔍 开始WebSocket并发冲突诊断...
2025-08-04 12:40:08,606 [INFO] 🧪 测试1: 多协程同时调用recv()
2025-08-04 12:40:08,607 [ERROR] ❌ worker_1 recv()失败: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 12:40:08,607 [ERROR] ❌ worker_2 recv()失败: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 12:40:10,609 [ERROR] ❌ 检测到WebSocket并发冲突
2025-08-04 12:40:10,610 [INFO] 🧪 测试2: 健康监控与主循环冲突
2025-08-04 12:40:11,614 [ERROR] 健康监控并发冲突: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 12:40:15,612 [ERROR] ❌ 检测到健康监控并发冲突
2025-08-04 12:40:15,612 [INFO] 🧪 测试3: 连接池管理器冲突
2025-08-04 12:40:15,612 [ERROR] 连接池管理器并发冲突: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 12:40:15,612 [ERROR] 连接池管理器并发冲突: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 12:40:17,614 [ERROR] ❌ 连接池管理器并发冲突: 2个
2025-08-04 12:40:17,615 [INFO] 🧪 测试4: 验证单一消费者模式
2025-08-04 12:40:21,618 [INFO] ✅ 单一消费者模式正常
2025-08-04 12:40:21,618 [INFO] 🧪 测试5: 检测实际系统并发问题
2025-08-04 12:40:21,635 [INFO] 📊 日志分析: 发现3200个并发冲突错误
2025-08-04 12:40:21,635 [ERROR] ❌ 实际系统并发冲突: 3200个
