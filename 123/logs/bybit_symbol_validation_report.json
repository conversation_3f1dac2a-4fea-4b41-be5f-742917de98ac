{"summary": {"total_symbols_tested": 8, "spot_available_count": 8, "futures_available_count": 7, "spot_availability_rate": 100.0, "futures_availability_rate": 87.5}, "bybit_instruments": {"total_spot_symbols": 660, "total_futures_symbols": 500}, "validation_results": [{"symbol": "BTC-USDT", "standard_format": "BTC-USDT", "bybit_format": "BTCUSDT", "spot_available": true, "futures_available": true, "error_message": ""}, {"symbol": "ETH-USDT", "standard_format": "ETH-USDT", "bybit_format": "ETHUSDT", "spot_available": true, "futures_available": true, "error_message": ""}, {"symbol": "ADA-USDT", "standard_format": "ADA-USDT", "bybit_format": "ADAUSDT", "spot_available": true, "futures_available": true, "error_message": ""}, {"symbol": "DOT-USDT", "standard_format": "DOT-USDT", "bybit_format": "DOTUSDT", "spot_available": true, "futures_available": true, "error_message": ""}, {"symbol": "SHIB-USDT", "standard_format": "SHIB-USDT", "bybit_format": "SHIBUSDT", "spot_available": true, "futures_available": false, "error_message": "Bybit不支持SHIB-USDT期货交易"}, {"symbol": "DOGE-USDT", "standard_format": "DOGE-USDT", "bybit_format": "DOGEUSDT", "spot_available": true, "futures_available": true, "error_message": ""}, {"symbol": "SOL-USDT", "standard_format": "SOL-USDT", "bybit_format": "SOLUSDT", "spot_available": true, "futures_available": true, "error_message": ""}, {"symbol": "AVAX-USDT", "standard_format": "AVAX-USDT", "bybit_format": "AVAXUSDT", "spot_available": true, "futures_available": true, "error_message": ""}], "unsupported_symbols": {"futures": ["SHIB-USDT"], "spot": []}, "recommendations": ["🔧 从期货套利配置中移除不支持的交易对: SHIB-USDT", "🔧 在系统启动前添加交易对验证机制，自动过滤不支持的交易对", "🔧 特别注意: SHIB在Bybit上没有期货合约，这是导致'symbol invalid'错误的根本原因", "🔧 修改TARGET_SYMBOLS配置，移除SHIB-USDT或为其添加交易所过滤规则", "🔧 优化错误处理: 将'交易对不存在'从ERROR级别降为DEBUG级别", "🔧 实施交易对预验证: 在WebSocket订阅前验证交易对是否存在", "🔧 添加交易所特定的交易对映射表", "🔧 实施优雅降级: 当某个交易所不支持特定交易对时，自动跳过而不报错"]}