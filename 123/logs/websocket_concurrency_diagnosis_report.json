{"summary": {"total_tests": 5, "successful_tests": 1, "failed_tests": 4, "concurrent_conflicts_detected": 4, "success_rate": 20.0}, "test_results": [{"test_name": "multiple_recv_calls", "success": false, "concurrent_recv_detected": true, "error_message": "检测到多个协程同时调用recv()", "error_type": "concurrent_recv", "duration_seconds": 2.0033342838287354, "recv_call_count": 0}, {"test_name": "health_monitor_conflict", "success": false, "concurrent_recv_detected": true, "error_message": "健康监控与主循环发生并发冲突", "error_type": "health_monitor_conflict", "duration_seconds": 5.001804828643799, "recv_call_count": 0}, {"test_name": "connection_pool_conflict", "success": false, "concurrent_recv_detected": true, "error_message": "连接池管理器检测到2个并发冲突", "error_type": "connection_pool_conflict", "duration_seconds": 2.002901077270508, "recv_call_count": 0}, {"test_name": "single_consumer_pattern", "success": true, "concurrent_recv_detected": false, "error_message": "", "error_type": "", "duration_seconds": 4.003605842590332, "recv_call_count": 0}, {"test_name": "real_system_concurrency", "success": false, "concurrent_recv_detected": true, "error_message": "日志中发现3200个并发冲突错误", "error_type": "real_system_conflict", "duration_seconds": 0.01643681526184082, "recv_call_count": 3200}], "recommendations": ["🔧 实施单一消费者模式：确保每个WebSocket连接只有一个协程调用recv()", "🔧 使用消息队列：将接收到的消息放入队列，由多个处理协程消费", "🔧 禁用健康监控任务：避免健康监控与主消息循环的并发冲突", "🔧 重构连接池管理器：避免直接操作WebSocket连接", "🔧 添加并发控制：使用锁或信号量控制WebSocket访问", "🔧 修复健康监控：将健康监控逻辑与WebSocket接收分离", "🔧 修复连接池：重新设计连接池管理器避免直接WebSocket操作"]}